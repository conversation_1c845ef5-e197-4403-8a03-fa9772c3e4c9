<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RetroAnalytica - Energy Performance Analytics Demo</title>
    <script src='https://api.tiles.mapbox.com/mapbox-gl-js/v2.10.0/mapbox-gl.js'></script>
    <link href="https://api.mapbox.com/mapbox-gl-js/v2.10.0/mapbox-gl.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background-color: #f8fafc;
            color: #334155;
        }

        .header {
            background: linear-gradient(135deg, #10069f 0%, #1a0fb8 100%);
            color: white;
            padding: 1rem 2rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
            font-weight: 300;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .products-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .product-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .product-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1);
        }

        .product-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e2e8f0;
        }

        .product-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: #10069f;
        }

        .product-description {
            color: #64748b;
            line-height: 1.6;
        }

        .map-container {
            height: 400px;
            position: relative;
        }

        .map {
            width: 100%;
            height: 100%;
        }

        .legend {
            background-color: white;
            border-radius: 8px;
            bottom: 15px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            font: 12px/16px 'Inter', sans-serif;
            padding: 12px;
            position: absolute;
            right: 15px;
            z-index: 1;
            max-width: 200px;
        }

        .legend h4 {
            margin: 0 0 8px;
            font-weight: 600;
            color: #10069f;
            font-size: 13px;
        }

        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 4px;
        }

        .legend-color {
            display: inline-block;
            height: 12px;
            width: 12px;
            margin-right: 8px;
            border-radius: 2px;
        }

        .controls {
            position: absolute;
            top: 15px;
            right: 15px;
            z-index: 1;
            background: white;
            padding: 8px 12px;
            border-radius: 8px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .controls label {
            font-weight: 500;
            font-size: 12px;
            color: #374151;
            margin-right: 8px;
        }

        .controls select {
            border: 1px solid #d1d5db;
            border-radius: 4px;
            padding: 4px 8px;
            font-size: 12px;
            background: white;
        }

        .footer {
            text-align: center;
            padding: 2rem;
            color: #64748b;
            border-top: 1px solid #e2e8f0;
            margin-top: 2rem;
        }

        @media (max-width: 768px) {
            .products-grid {
                grid-template-columns: 1fr;
            }
            
            .container {
                padding: 1rem;
            }
            
            .header {
                padding: 1rem;
            }
            
            .header h1 {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <h1>RetroAnalytica</h1>
        <p>Advanced Energy Performance Analytics for Postcode-Level Insights</p>
    </header>

    <div class="container">
        <div class="products-grid">
            <!-- Regional Analysis Product -->
            <div class="product-card">
                <div class="product-header">
                    <h2 class="product-title">Regional Energy Analysis</h2>
                    <p class="product-description">
                        Comprehensive energy saving potential analysis at regional level, 
                        providing strategic insights for large-scale energy efficiency planning.
                    </p>
                </div>
                <div class="map-container">
                    <div id="regional-map" class="map"></div>
                    <div class="legend" id="regional-legend"></div>
                </div>
            </div>

            <!-- Postcode Analysis Product -->
            <div class="product-card">
                <div class="product-header">
                    <h2 class="product-title">Postcode Energy Analysis</h2>
                    <p class="product-description">
                        Detailed postcode-level energy performance metrics, enabling 
                        precise targeting for energy efficiency improvements and investments.
                    </p>
                </div>
                <div class="map-container">
                    <div id="postcode-map" class="map"></div>
                    <div class="legend" id="postcode-legend"></div>
                </div>
            </div>
        </div>
    </div>

    <footer class="footer">
        <p>&copy; 2024 RetroAnalytica. Advanced analytics for energy performance optimization.</p>
    </footer>

    <script>
        // Mapbox access token
        mapboxgl.accessToken = 'pk.eyJ1IjoibWFvcmFuIiwiYSI6ImNrOG5qOXpmMjBxZ20zZnBlMnBmOTJpYWMifQ.fivZjT97qRobuXt3CVd0eA';

        // Legend configurations
        const legends = {
            regional: `
                <h4>Energy Saving Potential</h4>
                <div class="legend-item"><span class="legend-color" style="background-color: #2670c5;"></span> £2.5M</div>
                <div class="legend-item"><span class="legend-color" style="background-color: #0abaff;"></span> £24.4M</div>
                <div class="legend-item"><span class="legend-color" style="background-color: #5cd6ff;"></span> £46.3M</div>
                <div class="legend-item"><span class="legend-color" style="background-color: #f5f5f5;"></span> £90.2M</div>
                <div class="legend-item"><span class="legend-color" style="background-color: #ff968f;"></span> £134.1M</div>
                <div class="legend-item"><span class="legend-color" style="background-color: #d93f44;"></span> £177.9M</div>
            `,
            postcode: `
                <h4>Energy Saving Potential</h4>
                <div class="legend-item"><span class="legend-color" style="background-color: #2670c5;"></span> £155.4K</div>
                <div class="legend-item"><span class="legend-color" style="background-color: #0abaff;"></span> £492.5K</div>
                <div class="legend-item"><span class="legend-color" style="background-color: #5cd6ff;"></span> £673.2K</div>
                <div class="legend-item"><span class="legend-color" style="background-color: #f5f5f5;"></span> £820.9K</div>
                <div class="legend-item"><span class="legend-color" style="background-color: #ff968f;"></span> £1.02M</div>
                <div class="legend-item"><span class="legend-color" style="background-color: #d93f44;"></span> £3.46M</div>
            `
        };

        // Initialize Regional Map
        const regionalMap = new mapboxgl.Map({
            container: 'regional-map',
            style: 'mapbox://styles/maoran/clfyduzd1009d01nooynolt7b',
            center: [-3.656229527123442, 53.560791526121],
            zoom: 6
        });

        // Initialize Postcode Map
        const postcodeMap = new mapboxgl.Map({
            container: 'postcode-map',
            style: 'mapbox://styles/maoran/clwaku6bv004101pnh8voghhf',
            center: [-3.656229527123442, 53.560791526121],
            zoom: 6
        });

        // Add navigation controls
        regionalMap.addControl(new mapboxgl.NavigationControl());
        postcodeMap.addControl(new mapboxgl.NavigationControl());

        // Set legends
        document.getElementById('regional-legend').innerHTML = legends.regional;
        document.getElementById('postcode-legend').innerHTML = legends.postcode;

        // Add popup functionality for Regional Map
        regionalMap.on('load', () => {
            const popup = new mapboxgl.Popup({
                closeButton: false,
                closeOnClick: false
            });

            regionalMap.on('click', 'choropleth-fill', function(e) {
                const properties = e.features[0].properties;
                const popupKeys = ['AreaName', 'PostArea', 'savings'];
                let description = "<h3>Regional Data</h3>";
                
                popupKeys.forEach(function(key) {
                    if (properties[key] !== undefined) {
                        const value = key === 'savings' ? `£${Number(properties[key]).toLocaleString()}` : properties[key];
                        description += `<div><strong>${key}:</strong> ${value}</div>`;
                    }
                });

                new mapboxgl.Popup()
                    .setLngLat(e.lngLat)
                    .setHTML(description)
                    .addTo(regionalMap);
            });

            regionalMap.on('mousemove', (e) => {
                const features = regionalMap.queryRenderedFeatures(e.point);
                regionalMap.getCanvas().style.cursor = features.length ? 'pointer' : '';
            });
        });

        // Add popup functionality for Postcode Map
        postcodeMap.on('load', () => {
            const popup = new mapboxgl.Popup({
                closeButton: false,
                closeOnClick: false
            });

            postcodeMap.on('click', 'choropleth-fill', function(e) {
                const properties = e.features[0].properties;
                const popupKeys = ['EnergySaving'];
                let description = "<h3>Postcode Data</h3>";
                
                popupKeys.forEach(function(key) {
                    if (properties[key] !== undefined) {
                        const value = `£${Number(properties[key]).toLocaleString()}`;
                        description += `<div><strong>Energy Saving:</strong> ${value}</div>`;
                    }
                });

                new mapboxgl.Popup()
                    .setLngLat(e.lngLat)
                    .setHTML(description)
                    .addTo(postcodeMap);
            });

            postcodeMap.on('mousemove', (e) => {
                const features = postcodeMap.queryRenderedFeatures(e.point);
                postcodeMap.getCanvas().style.cursor = features.length ? 'pointer' : '';
            });
        });
    </script>
</body>
</html>
