// RetroAnalytica Demo Application
// Mapbox access token
mapboxgl.accessToken = 'pk.eyJ1IjoibWFvcmFuIiwiYSI6ImNrOG5qOXpmMjBxZ20zZnBlMnBmOTJpYWMifQ.fivZjT97qRobuXt3CVd0eA';

// Application state
const appState = {
    syncMaps: false,
    currentRegionalData: null,
    currentPostcodeData: null,
    isLoading: false
};

// Legend configurations
const legends = {
    regional: `
        <h4>Energy Saving Potential</h4>
        <div class="legend-item">
            <span class="legend-color" style="background-color: #2670c5;"></span>
            <span>£2.5M</span>
        </div>
        <div class="legend-item">
            <span class="legend-color" style="background-color: #0abaff;"></span>
            <span>£24.4M</span>
        </div>
        <div class="legend-item">
            <span class="legend-color" style="background-color: #5cd6ff;"></span>
            <span>£46.3M</span>
        </div>
        <div class="legend-item">
            <span class="legend-color" style="background-color: #f5f5f5;"></span>
            <span>£90.2M</span>
        </div>
        <div class="legend-item">
            <span class="legend-color" style="background-color: #ff968f;"></span>
            <span>£134.1M</span>
        </div>
        <div class="legend-item">
            <span class="legend-color" style="background-color: #d93f44;"></span>
            <span>£177.9M</span>
        </div>
    `,
    postcode: `
        <h4>Energy Saving Potential</h4>
        <div class="legend-item">
            <span class="legend-color" style="background-color: #2670c5;"></span>
            <span>£155.4K</span>
        </div>
        <div class="legend-item">
            <span class="legend-color" style="background-color: #0abaff;"></span>
            <span>£492.5K</span>
        </div>
        <div class="legend-item">
            <span class="legend-color" style="background-color: #5cd6ff;"></span>
            <span>£673.2K</span>
        </div>
        <div class="legend-item">
            <span class="legend-color" style="background-color: #f5f5f5;"></span>
            <span>£820.9K</span>
        </div>
        <div class="legend-item">
            <span class="legend-color" style="background-color: #ff968f;"></span>
            <span>£1.02M</span>
        </div>
        <div class="legend-item">
            <span class="legend-color" style="background-color: #d93f44;"></span>
            <span>£3.46M</span>
        </div>
    `
};

// Utility functions
function formatCurrency(value) {
    return `£${Number(value).toLocaleString()}`;
}

function showLoadingState(mapContainer) {
    const loader = document.createElement('div');
    loader.className = 'loading-overlay';
    loader.innerHTML = '<div class="spinner"></div><p>Loading data...</p>';
    mapContainer.appendChild(loader);
}

function hideLoadingState(mapContainer) {
    const loader = mapContainer.querySelector('.loading-overlay');
    if (loader) {
        loader.remove();
    }
}

// Initialize maps
function initializeMaps() {
    // Initialize Regional Map
    const regionalMap = new mapboxgl.Map({
        container: 'regional-map',
        style: 'mapbox://styles/maoran/clfyduzd1009d01nooynolt7b',
        center: [-3.656229527123442, 53.560791526121],
        zoom: 6,
        attributionControl: false
    });

    // Initialize Postcode Map
    const postcodeMap = new mapboxgl.Map({
        container: 'postcode-map',
        style: 'mapbox://styles/maoran/clwaku6bv004101pnh8voghhf',
        center: [-3.656229527123442, 53.560791526121],
        zoom: 6,
        attributionControl: false
    });

    // Add navigation controls
    regionalMap.addControl(new mapboxgl.NavigationControl(), 'top-left');
    postcodeMap.addControl(new mapboxgl.NavigationControl(), 'top-left');

    // Add attribution controls at bottom
    regionalMap.addControl(new mapboxgl.AttributionControl({
        compact: true
    }), 'bottom-left');
    
    postcodeMap.addControl(new mapboxgl.AttributionControl({
        compact: true
    }), 'bottom-left');

    // Set legends
    document.getElementById('regional-legend').innerHTML = legends.regional;
    document.getElementById('postcode-legend').innerHTML = legends.postcode;

    return { regionalMap, postcodeMap };
}

// Setup map interactions
function setupMapInteractions(regionalMap, postcodeMap) {
    // Regional Map interactions
    regionalMap.on('load', () => {
        setupRegionalMapEvents(regionalMap);
    });

    // Postcode Map interactions
    postcodeMap.on('load', () => {
        setupPostcodeMapEvents(postcodeMap);
    });

    // Setup map synchronization
    setupMapSynchronization(regionalMap, postcodeMap);
}

function setupRegionalMapEvents(map) {
    // Click event for regional map
    map.on('click', 'choropleth-fill', function(e) {
        const properties = e.features[0].properties;
        appState.currentRegionalData = properties;
        
        const popupKeys = ['AreaName', 'PostArea', 'savings'];
        let description = "<h3>Regional Analysis</h3>";
        
        popupKeys.forEach(function(key) {
            if (properties[key] !== undefined) {
                let label = key;
                let value = properties[key];
                
                if (key === 'savings') {
                    label = 'Energy Savings';
                    value = formatCurrency(value);
                } else if (key === 'AreaName') {
                    label = 'Area';
                } else if (key === 'PostArea') {
                    label = 'Postal Area';
                }
                
                description += `<div><strong>${label}:</strong> ${value}</div>`;
            }
        });

        new mapboxgl.Popup({
            closeButton: true,
            closeOnClick: true,
            maxWidth: '300px'
        })
            .setLngLat(e.lngLat)
            .setHTML(description)
            .addTo(map);
    });

    // Hover effects
    map.on('mouseenter', 'choropleth-fill', function() {
        map.getCanvas().style.cursor = 'pointer';
    });

    map.on('mouseleave', 'choropleth-fill', function() {
        map.getCanvas().style.cursor = '';
    });
}

function setupPostcodeMapEvents(map) {
    // Click event for postcode map
    map.on('click', 'choropleth-fill', function(e) {
        const properties = e.features[0].properties;
        appState.currentPostcodeData = properties;
        
        const popupKeys = ['EnergySaving'];
        let description = "<h3>Postcode Analysis</h3>";
        
        popupKeys.forEach(function(key) {
            if (properties[key] !== undefined) {
                const value = formatCurrency(properties[key]);
                description += `<div><strong>Energy Saving Potential:</strong> ${value}</div>`;
            }
        });

        // Add additional context if available
        if (properties.postcode) {
            description += `<div><strong>Postcode:</strong> ${properties.postcode}</div>`;
        }

        new mapboxgl.Popup({
            closeButton: true,
            closeOnClick: true,
            maxWidth: '300px'
        })
            .setLngLat(e.lngLat)
            .setHTML(description)
            .addTo(map);
    });

    // Hover effects
    map.on('mouseenter', 'choropleth-fill', function() {
        map.getCanvas().style.cursor = 'pointer';
    });

    map.on('mouseleave', 'choropleth-fill', function() {
        map.getCanvas().style.cursor = '';
    });
}

function setupMapSynchronization(regionalMap, postcodeMap) {
    let isMoving = false;

    function syncMapMovement(sourceMap, targetMap) {
        if (appState.syncMaps && !isMoving) {
            isMoving = true;
            targetMap.setCenter(sourceMap.getCenter());
            targetMap.setZoom(sourceMap.getZoom());
            setTimeout(() => { isMoving = false; }, 100);
        }
    }

    regionalMap.on('move', () => syncMapMovement(regionalMap, postcodeMap));
    postcodeMap.on('move', () => syncMapMovement(postcodeMap, regionalMap));
}

// Error handling
function handleMapError(error, mapType) {
    console.error(`Error loading ${mapType} map:`, error);
    
    const mapContainer = document.getElementById(`${mapType}-map`);
    const errorDiv = document.createElement('div');
    errorDiv.className = 'map-error';
    errorDiv.innerHTML = `
        <div style="padding: 2rem; text-align: center; color: #ef4444;">
            <h3>Unable to load ${mapType} map</h3>
            <p>Please check your internet connection and try again.</p>
            <button onclick="location.reload()" style="margin-top: 1rem; padding: 0.5rem 1rem; background: #3b82f6; color: white; border: none; border-radius: 4px; cursor: pointer;">
                Retry
            </button>
        </div>
    `;
    mapContainer.appendChild(errorDiv);
}

// Initialize application
function initializeApp() {
    try {
        const { regionalMap, postcodeMap } = initializeMaps();
        setupMapInteractions(regionalMap, postcodeMap);
        
        // Handle map load errors
        regionalMap.on('error', (e) => handleMapError(e, 'regional'));
        postcodeMap.on('error', (e) => handleMapError(e, 'postcode'));
        
        console.log('RetroAnalytica demo initialized successfully');
    } catch (error) {
        console.error('Failed to initialize RetroAnalytica demo:', error);
    }
}

// Start the application when DOM is loaded
document.addEventListener('DOMContentLoaded', initializeApp);

// Export for potential external use
window.RetroAnalytica = {
    appState,
    legends,
    formatCurrency
};
