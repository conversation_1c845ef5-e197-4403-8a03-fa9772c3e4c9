<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mapbox Tile Example</title>
    <script src='https://api.tiles.mapbox.com/mapbox-gl-js/v2.10.0/mapbox-gl.js'></script>
    <link href="https://api.mapbox.com/mapbox-gl-js/v2.10.0/mapbox-gl.css" rel="stylesheet">
    <link rel="stylesheet" href="https://api.mapbox.com/mapbox-gl-js/plugins/mapbox-gl-geocoder/v4.5.1/mapbox-gl-geocoder.css" type="text/css" />
    <style>
        body { margin: 0; padding: 0; }
        #map { position: absolute; top: 0; bottom: 0; width: 100%; }
        .legend {
            background-color: white;
            border-radius: 3px;
            bottom: 30px;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
            font: 12px/20px 'Helvetica Neue', Arial, Helvetica, sans-serif;
            padding: 10px;
            position: absolute;
            right: 10px;
            z-index: 1;
        }
        .legend h4 {
            margin: 0 0 10px;
        }
        .legend div {
            display: flex;
            align-items: center;
        }
        .legend div span {
            display: inline-block;
            height: 12px;
            width: 12px;
            margin-right: 5px;
        }

        .popup {
            background: white;
            border-radius: 3px;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
            padding: 10px;
            position: absolute;
            z-index: 1;
            pointer-events: none;
            transform: translate(-50%, -100%);
        }

        .map-switcher {
            position: absolute;
            right: 20px;
            bottom: 230px;
            z-index: 1;
            background: white;
            padding: 10px;
            border-radius: 3px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <div id="map"></div>
    <div class="legend" id="legend"></div>


    <div class="map-switcher">
        <label for="styleSelect" style="font-weight: 400; font-family: sans-serif;">Data Level:</label>
        <select id="styleSelect">
            <option value="mapbox://styles/maoran/clfyduzd1009d01nooynolt7b">Regional</option>
            <option value="mapbox://styles/maoran/clwaku6bv004101pnh8voghhf">Postcode</option>
        </select>
    </div>

    <script src="https://api.mapbox.com/mapbox-gl-js/v2.10.0/mapbox-gl.js"></script>
    <script>
        // Replace 'your_mapbox_access_token' with your actual Mapbox access token
        mapboxgl.accessToken = 'pk.eyJ1IjoibWFvcmFuIiwiYSI6ImNrOG5qOXpmMjBxZ20zZnBlMnBmOTJpYWMifQ.fivZjT97qRobuXt3CVd0eA';

        var currentStyle = 'mapbox://styles/maoran/clfyduzd1009d01nooynolt7b'
        
        const map = new mapboxgl.Map({
            container: 'map', // container ID
            style: currentStyle, // style URL
            center: [-3.656229527123442, 53.560791526121], // starting position [lng, lat]
            zoom: 6 // starting zoom
        });

        // console.log(map);

        // Add navigation controls to the map
        map.addControl(new mapboxgl.NavigationControl());

        // Add a popup that will be used to display feature info
        const popup = new mapboxgl.Popup({
            closeButton: false,
            closeOnClick: false
        });

        // Define legends for different styles
        const legends = {
            'mapbox://styles/maoran/clfyduzd1009d01nooynolt7b': `
                <h4>Energy Saving Potential (GBP)</h4>
                <div><span style="background-color: #2670c5;"></span> 2.5M</div>
                <div><span style="background-color: #0abaff;"></span> 24.4M</div>
                <div><span style="background-color: #5cd6ff;"></span> 46.3M</div>
                <div><span style="background-color: #f5f5f5;"></span> 90.2M</div>
                <div><span style="background-color: #ff968f;"></span> 134.1M</div>
                <div><span style="background-color: #d93f44;"></span> 177.9M</div>
            `,
            'mapbox://styles/maoran/clwaku6bv004101pnh8voghhf': `
                <h4>Energy Saving Potential (GBP)</h4>
                <div><span style="background-color: #2670c5;"></span> 155.4K</div>
                <div><span style="background-color: #0abaff;"></span> 492.5K</div>
                <div><span style="background-color: #5cd6ff;"></span> 673.2K</div>
                <div><span style="background-color: #f5f5f5;"></span> 820.9K</div>
                <div><span style="background-color: #ff968f;"></span> 1.02M</div>
                <div><span style="background-color: #d93f44;"></span> 3.46M</div>
            `,
        };


        // Set initial legend
        const legend = document.getElementById('legend');
        legend.innerHTML = legends[currentStyle];


// {"AreaName":"Lincoln","PostArea":"LN","savings":28868581}
        map.on('load', () => {
            // // Add a click event listener to the map
            // map.on('click', (e) => {
            //     // Query for the features under the mouse pointer
            //     const features = map.queryRenderedFeatures(e.point, {
            //         layers: ['choropleth-fill'] // Specify the layer(s) to be checked
            //     });

            //     // If there are features, display the first feature's properties in a popup
            //     if (features.length) {
            //         const feature = features[0];
            //         popup.setLngLat(e.lngLat)
            //             .setHTML(`<h3>${feature.properties.AreaName}</h3><p>${feature.properties.savings} GBP</p>`)
            //             .addTo(map);
            //     }
            // });

            // Display attributes in a popup on click

        const popupKeys = ['AreaName', 'PostArea', 'savings', 'EnergySaving']; // Add this near the top of your script

        map.on('click', 'choropleth-fill', function(e) {
            var properties = e.features[0].properties;
            var description = "<h3>Feature Properties</h3>";
            popupKeys.forEach(function(key) {
                if (properties[key] !== undefined) {
                    description += "<div><strong>" + key + ":</strong> " + properties[key] + "</div>";
                }
            });

            new mapboxgl.Popup()
                .setLngLat(e.lngLat)
                .setHTML(description)
                .addTo(map);
        });

            // Add a mousemove event listener to change the cursor when hovering over features
            map.on('mousemove', (e) => {
                const features = map.queryRenderedFeatures(e.point);
                map.getCanvas().style.cursor = features.length ? 'pointer' : '';
            });
        });

        // Handle the style switching
        const styleSelect = document.getElementById('styleSelect');

        styleSelect.addEventListener('change', (e) => {
            const selectedStyle = e.target.value;
            map.setStyle(selectedStyle);

            // Update the legend
            legend.innerHTML = legends[selectedStyle];

            map.once('styledata', () => {

                // Reapply the interaction handlers for the new style

                // map.on('click', 'choropleth-fill', function(e) {
                //     var properties = e.features[0].properties; // Access the properties of the clicked feature
                //     var description = "<h3>Feature Properties</h3>";
                //     for (var key in properties) {
                //         description += "<div><strong>" + key + ":</strong> " + properties[key] + "</div>";
                //     }

                //     new mapboxgl.Popup()
                //         .setLngLat(e.lngLat)
                //         .setHTML(description) // Set the description as the content of the popup
                //         .addTo(map);
                // });

                const popupKeysPostcode = ['EnergySaving']; // Add this near the top of your script

                map.on('click', 'choropleth-fill', function(e) {
                    var properties = e.features[0].properties;
                    var description = "<h3>Feature Properties</h3>";
                    popupKeysPostcode.forEach(function(key) {
                        if (properties[key] !== undefined) {
                            description += "<div><strong>" + key + ":</strong> " + properties[key] + "</div>";
                        }
                    });

                    new mapboxgl.Popup()
                        .setLngLat(e.lngLat)
                        .setHTML(description)
                        .addTo(map);
                });

                map.on('mousemove', (e) => {
                    const features = map.queryRenderedFeatures(e.point);
                    map.getCanvas().style.cursor = features.length ? 'pointer' : '';
                });
            });
        });
    </script>
</body>
</html>
