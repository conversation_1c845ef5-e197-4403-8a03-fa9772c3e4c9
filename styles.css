* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    background-color: #f8fafc;
    color: #334155;
    line-height: 1.6;
}

.header {
    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
    color: white;
    padding: 2rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.header-content {
    position: relative;
    z-index: 1;
    max-width: 1400px;
    margin: 0 auto;
}

.header h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    background: linear-gradient(45deg, #ffffff, #e0e7ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.header p {
    font-size: 1.2rem;
    opacity: 0.9;
    font-weight: 300;
    max-width: 600px;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem;
}

.intro-section {
    text-align: center;
    margin-bottom: 3rem;
}

.intro-section h2 {
    font-size: 2rem;
    font-weight: 600;
    color: #1e40af;
    margin-bottom: 1rem;
}

.intro-section p {
    font-size: 1.1rem;
    color: #64748b;
    max-width: 800px;
    margin: 0 auto;
}

.products-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 3rem;
}

.product-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid #e2e8f0;
}

.product-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    border-color: #3b82f6;
}

.product-header {
    padding: 2rem;
    border-bottom: 1px solid #e2e8f0;
    background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
}

.product-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    color: #1e40af;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.product-icon {
    width: 24px;
    height: 24px;
    background: linear-gradient(135deg, #3b82f6, #1e40af);
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.product-description {
    color: #64748b;
    line-height: 1.7;
    font-size: 1rem;
}

.product-stats {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #f1f5f9;
}

.stat {
    text-align: center;
    flex: 1;
}

.stat-value {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1e40af;
}

.stat-label {
    font-size: 0.875rem;
    color: #64748b;
    margin-top: 0.25rem;
}

.map-container {
    height: 450px;
    position: relative;
    background: #f8fafc;
}

.map {
    width: 100%;
    height: 100%;
    border-radius: 0 0 16px 16px;
}

.legend {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    bottom: 20px;
    box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.1);
    font: 12px/16px 'Inter', sans-serif;
    padding: 16px;
    position: absolute;
    right: 20px;
    z-index: 1;
    max-width: 220px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.legend h4 {
    margin: 0 0 12px;
    font-weight: 600;
    color: #1e40af;
    font-size: 14px;
    text-align: center;
    padding-bottom: 8px;
    border-bottom: 1px solid #e2e8f0;
}

.legend-item {
    display: flex;
    align-items: center;
    margin-bottom: 6px;
    padding: 2px 0;
}

.legend-color {
    display: inline-block;
    height: 14px;
    width: 14px;
    margin-right: 10px;
    border-radius: 3px;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.legend-item span:last-child {
    font-weight: 500;
    color: #374151;
}

.controls {
    position: absolute;
    top: 20px;
    right: 20px;
    z-index: 1;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 12px 16px;
    border-radius: 12px;
    box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.controls label {
    font-weight: 500;
    font-size: 13px;
    color: #374151;
    margin-right: 10px;
}

.controls select {
    border: 1px solid #d1d5db;
    border-radius: 6px;
    padding: 6px 10px;
    font-size: 13px;
    background: white;
    color: #374151;
    font-weight: 500;
    min-width: 120px;
}

.controls select:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.features-section {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    margin: 2rem 0;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-top: 2rem;
}

.feature {
    text-align: center;
    padding: 1.5rem;
    border-radius: 12px;
    background: #f8fafc;
    border: 1px solid #e2e8f0;
}

.feature-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #3b82f6, #1e40af);
    border-radius: 12px;
    margin: 0 auto 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
}

.feature h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #1e40af;
    margin-bottom: 0.5rem;
}

.feature p {
    color: #64748b;
    font-size: 0.9rem;
}

.footer {
    text-align: center;
    padding: 3rem 2rem;
    color: #64748b;
    border-top: 1px solid #e2e8f0;
    margin-top: 3rem;
    background: white;
}

.footer p {
    font-size: 0.9rem;
}

/* Custom popup styles */
.mapboxgl-popup-content {
    border-radius: 12px;
    box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
    padding: 16px;
    font-family: 'Inter', sans-serif;
    max-width: 300px;
}

.mapboxgl-popup-content h3 {
    color: #1e40af;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 8px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e2e8f0;
}

.mapboxgl-popup-content div {
    margin-bottom: 4px;
    font-size: 0.9rem;
}

.mapboxgl-popup-content strong {
    color: #374151;
    font-weight: 500;
}

.mapboxgl-popup-close-button {
    color: #64748b;
    font-size: 18px;
    padding: 4px;
}

.mapboxgl-popup-close-button:hover {
    color: #1e40af;
    background: #f1f5f9;
    border-radius: 4px;
}

/* Loading and error states */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(248, 250, 252, 0.9);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    border-radius: 0 0 16px 16px;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e2e8f0;
    border-top: 4px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-overlay p {
    color: #64748b;
    font-weight: 500;
    font-size: 0.9rem;
}

.map-error {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: #fef2f2;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    border-radius: 0 0 16px 16px;
    border: 1px solid #fecaca;
}

.map-error h3 {
    color: #dc2626;
    margin-bottom: 0.5rem;
}

.map-error p {
    color: #7f1d1d;
    margin-bottom: 1rem;
}

/* Enhanced map controls */
.mapboxgl-ctrl-group {
    border-radius: 8px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.mapboxgl-ctrl-group button {
    border-radius: 0;
}

.mapboxgl-ctrl-group button:first-child {
    border-radius: 8px 8px 0 0;
}

.mapboxgl-ctrl-group button:last-child {
    border-radius: 0 0 8px 8px;
}

.mapboxgl-ctrl-group button:only-child {
    border-radius: 8px;
}

/* Attribution styling */
.mapboxgl-ctrl-attrib {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    border-radius: 6px;
    font-size: 11px;
    padding: 4px 8px;
}

@media (max-width: 768px) {
    .products-grid {
        grid-template-columns: 1fr;
    }
    
    .container {
        padding: 1rem;
    }
    
    .header {
        padding: 1.5rem 1rem;
    }
    
    .header h1 {
        font-size: 2rem;
    }
    
    .header p {
        font-size: 1rem;
    }
    
    .product-header {
        padding: 1.5rem;
    }
    
    .map-container {
        height: 350px;
    }
    
    .legend {
        bottom: 15px;
        right: 15px;
        padding: 12px;
        max-width: 180px;
    }
    
    .controls {
        top: 15px;
        right: 15px;
        padding: 8px 12px;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
    }
}
