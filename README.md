# RetroAnalytica Demo

A comprehensive web application showcasing RetroAnalytica's energy performance analytics platform with dual-component visualization of postcode-level energy metrics.

## Overview

RetroAnalytica provides advanced energy performance analytics through two complementary approaches:

1. **Regional Energy Analysis** - Strategic insights for large-scale energy efficiency planning
2. **Postcode Energy Analysis** - Granular, precise targeting for energy efficiency improvements

## Features

### 🗺️ Interactive Maps
- **Dual Map Interface**: Side-by-side comparison of regional and postcode-level data
- **Mapbox Integration**: High-performance, interactive maps with custom tilesets
- **Real-time Interaction**: Click on any area to view detailed energy saving potential
- **Responsive Design**: Optimized for desktop and mobile viewing

### 📊 Data Visualization
- **Color-coded Legends**: Clear visual representation of energy saving potential ranges
- **Interactive Popups**: Detailed information on hover and click
- **Professional Styling**: Modern, clean interface with RetroAnalytica branding

### 🎯 Analytics Capabilities
- **Regional Level**: Energy savings from £2.5M to £177.9M
- **Postcode Level**: Energy savings from £155.4K to £3.46M
- **Comprehensive Coverage**: UK-wide postcode and regional analysis
- **Strategic Planning**: Tools for both macro and micro-level decision making

## File Structure

```
RetroAnalytica-demo/
├── index.html          # Simple version of the demo
├── demo.html           # Enhanced version with full features
├── styles.css          # Comprehensive styling
├── app.js              # JavaScript application logic
├── decarbonizeMap.html # Original implementation
└── README.md           # This file
```

## Getting Started

### Prerequisites
- Modern web browser with JavaScript enabled
- Internet connection for Mapbox tiles and fonts

### Running the Demo

1. **Simple Version**: Open `index.html` in your web browser
2. **Enhanced Version**: Open `demo.html` in your web browser (recommended)

### Local Development

For local development with a web server:

```bash
# Using Python 3
python -m http.server 8000

# Using Node.js (if you have http-server installed)
npx http-server

# Using PHP
php -S localhost:8000
```

Then navigate to `http://localhost:8000/demo.html`

## Technical Details

### Technologies Used
- **Mapbox GL JS v2.10.0**: Interactive maps and data visualization
- **HTML5 & CSS3**: Modern web standards
- **Vanilla JavaScript**: No framework dependencies
- **Google Fonts (Inter)**: Professional typography

### Map Configuration
- **Regional Tileset**: `mapbox://styles/maoran/clfyduzd1009d01nooynolt7b`
- **Postcode Tileset**: `mapbox://styles/maoran/clwaku6bv004101pnh8voghhf`
- **Default Center**: Manchester, UK (-3.656, 53.561)
- **Default Zoom**: Level 6

### Data Properties
- **Regional Data**: AreaName, PostArea, savings
- **Postcode Data**: EnergySaving
- **Currency Format**: British Pounds (£) with thousand separators

## Customization

### Styling
Modify `styles.css` to customize:
- Color schemes and branding
- Layout and spacing
- Typography and fonts
- Interactive elements

### Map Configuration
Update `app.js` to modify:
- Map center and zoom levels
- Tileset URLs
- Legend configurations
- Popup content and formatting

### Data Integration
To integrate with different data sources:
1. Update tileset URLs in the map initialization
2. Modify popup key mappings for your data structure
3. Adjust legend values and colors to match your data ranges

## Browser Support

- Chrome 60+
- Firefox 60+
- Safari 12+
- Edge 79+

## Performance Considerations

- Maps are optimized for fast loading with Mapbox vector tiles
- Responsive design ensures good performance on mobile devices
- Minimal JavaScript dependencies for faster page loads
- Efficient CSS with modern layout techniques

## Future Enhancements

Potential improvements for the demo:
- [ ] Search functionality for specific postcodes/regions
- [ ] Data export capabilities
- [ ] Comparison tools between regions
- [ ] Time-series analysis
- [ ] Advanced filtering options
- [ ] Integration with external APIs

## Support

For questions about RetroAnalytica or this demo:
- Review the code documentation in `app.js`
- Check browser console for any error messages
- Ensure stable internet connection for map tiles

## License

This demo is created for RetroAnalytica demonstration purposes.

---

**RetroAnalytica** - Advanced analytics for energy performance optimization.
