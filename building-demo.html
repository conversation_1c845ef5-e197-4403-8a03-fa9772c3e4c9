<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RetroAnalytica - Building & Postcode Analytics</title>
    <script src='https://api.tiles.mapbox.com/mapbox-gl-js/v2.10.0/mapbox-gl.js'></script>
    <link href="https://api.mapbox.com/mapbox-gl-js/v2.10.0/mapbox-gl.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
    <style>
        .address-search-container {
            background: white;
            border-radius: 16px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            transition: all 0.3s ease;
            border: 1px solid #e2e8f0;
            height: 450px;
            display: flex;
            flex-direction: column;
        }

        .search-header {
            padding: 2rem;
            border-bottom: 1px solid #e2e8f0;
            background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
        }

        .search-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 0.75rem;
            color: #BB2649;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .search-icon {
            width: 24px;
            height: 24px;
            background: linear-gradient(135deg, #BB2649, #a01e3a);
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            font-weight: bold;
        }

        .search-description {
            color: #64748b;
            line-height: 1.7;
            font-size: 1rem;
            margin-bottom: 1rem;
        }

        .search-form {
            display: flex;
            gap: 0.75rem;
            margin-top: 1rem;
        }

        .search-input {
            flex: 1;
            padding: 0.75rem 1rem;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.2s ease;
        }

        .search-input:focus {
            outline: none;
            border-color: #BB2649;
            box-shadow: 0 0 0 3px rgba(187, 38, 73, 0.1);
        }

        .search-button {
            padding: 0.75rem 1.5rem;
            background: linear-gradient(135deg, #BB2649, #a01e3a);
            color: white;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s ease;
        }

        .search-button:hover {
            transform: translateY(-1px);
        }

        .results-container {
            flex: 1;
            padding: 1.5rem 2rem;
            overflow-y: auto;
        }

        .building-result {
            background: #f8fafc;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            border: 1px solid #e2e8f0;
        }

        .building-address {
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 1rem;
            font-size: 1.1rem;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .metric-item {
            text-align: center;
            background: white;
            padding: 1rem;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
        }

        .metric-value {
            font-size: 1.25rem;
            font-weight: 600;
            color: #BB2649;
            margin-bottom: 0.25rem;
        }

        .metric-label {
            font-size: 0.875rem;
            color: #64748b;
        }

        .retrofit-insights {
            background: white;
            border-radius: 8px;
            padding: 1.25rem;
            border: 1px solid #e2e8f0;
        }

        .insights-title {
            font-weight: 600;
            color: #BB2649;
            margin-bottom: 0.75rem;
            font-size: 1rem;
        }

        .insight-item {
            display: flex;
            align-items: flex-start;
            gap: 0.5rem;
            margin-bottom: 0.75rem;
            font-size: 0.9rem;
            line-height: 1.5;
        }

        .insight-icon {
            width: 16px;
            height: 16px;
            background: #BB2649;
            border-radius: 50%;
            flex-shrink: 0;
            margin-top: 0.125rem;
        }

        .no-results {
            text-align: center;
            color: #64748b;
            padding: 2rem;
            font-style: italic;
        }

        .loading-search {
            text-align: center;
            padding: 2rem;
            color: #64748b;
        }

        .search-spinner {
            width: 24px;
            height: 24px;
            border: 3px solid #e2e8f0;
            border-top: 3px solid #BB2649;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-content">
            <h1>RetroAnalytica</h1>
            <p>Advanced Energy Performance Analytics for Postcode & Building level Insights</p>
        </div>
    </header>

    <div class="container">
        <div class="intro-section">
            <h2>Building-Level & Postcode Analytics Platform</h2>
            <p>
                Discover energy saving opportunities with our advanced analytics platform. 
                Search for specific buildings to get detailed energy metrics and retrofit recommendations, 
                or explore postcode-level data for broader insights across geographic areas.
            </p>
        </div>

        <div class="products-grid">
            <!-- Building Search Component -->
            <div class="address-search-container">
                <div class="search-header">
                    <h2 class="search-title">
                        <span class="search-icon">🏢</span>
                        Building Energy Analysis
                    </h2>
                    <p class="search-description">
                        Search for any UK address to get detailed energy performance metrics, 
                        retrofit recommendations, and potential savings for individual buildings.
                    </p>
                    <div class="search-form">
                        <input 
                            type="text" 
                            class="search-input" 
                            placeholder="Enter building address (e.g., 10 Downing Street, London)"
                            id="address-input"
                        >
                        <button class="search-button" onclick="searchBuilding()">Search</button>
                    </div>
                </div>
                <div class="results-container" id="search-results">
                    <div class="no-results">
                        Enter an address above to get detailed building energy analysis
                    </div>
                </div>
            </div>

            <!-- Postcode Analysis Product -->
            <div class="product-card">
                <div class="product-header">
                    <h2 class="product-title">
                        <span class="product-icon">P</span>
                        Postcode Energy Analysis
                    </h2>
                    <p class="product-description">
                        Detailed postcode-level energy performance metrics, enabling 
                        precise targeting for energy efficiency improvements and investments 
                        with granular geographic precision.
                    </p>
                    <div class="product-stats">
                        <div class="stat">
                            <div class="stat-value">£3.46M</div>
                            <div class="stat-label">Max Potential</div>
                        </div>
                        <div class="stat">
                            <div class="stat-value">Postcode</div>
                            <div class="stat-label">Precision</div>
                        </div>
                        <div class="stat">
                            <div class="stat-value">Targeted</div>
                            <div class="stat-label">Insights</div>
                        </div>
                    </div>
                </div>
                <div class="map-container">
                    <div id="postcode-map" class="map"></div>
                    <div class="legend" id="postcode-legend"></div>
                </div>
            </div>
        </div>

        <div class="features-section">
            <h2 style="text-align: center; color: #BB2649; margin-bottom: 1rem;">Platform Features</h2>
            <p style="text-align: center; color: #64748b; margin-bottom: 2rem;">
                Comprehensive tools for building-level and area-wide energy analysis
            </p>
            <div class="features-grid">
                <div class="feature">
                    <div class="feature-icon">🔍</div>
                    <h3>Address Search</h3>
                    <p>Find any UK building and get instant energy performance data</p>
                </div>
                <div class="feature">
                    <div class="feature-icon">🏠</div>
                    <h3>Building Insights</h3>
                    <p>Detailed retrofit recommendations and energy saving potential</p>
                </div>
                <div class="feature">
                    <div class="feature-icon">📍</div>
                    <h3>Postcode Mapping</h3>
                    <p>Interactive maps showing energy performance across postcodes</p>
                </div>
                <div class="feature">
                    <div class="feature-icon">💡</div>
                    <h3>Smart Recommendations</h3>
                    <p>AI-powered suggestions for energy efficiency improvements</p>
                </div>
            </div>
        </div>
    </div>

    <footer class="footer">
        <p>&copy; 2024 RetroAnalytica. Advanced analytics for energy performance optimization.</p>
    </footer>

    <script>
        // Mapbox access token
        mapboxgl.accessToken = 'pk.eyJ1IjoibWFvcmFuIiwiYSI6ImNrOG5qOXpmMjBxZ20zZnBlMnBmOTJpYWMifQ.fivZjT97qRobuXt3CVd0eA';

        // Sample building data for demonstration
        const sampleBuildings = {
            "10 downing street": {
                address: "10 Downing Street, Westminster, London SW1A 2AA",
                metrics: {
                    epcRating: "D",
                    annualCost: "£2,450",
                    co2Emissions: "4.2t",
                    savingPotential: "£850"
                },
                insights: [
                    "Install double glazing to reduce heat loss by 15%",
                    "Upgrade heating system to heat pump for 30% efficiency gain",
                    "Add roof insulation to save £200 annually",
                    "Smart thermostat could reduce bills by 8%"
                ]
            },
            "buckingham palace": {
                address: "Buckingham Palace, Westminster, London SW1A 1AA",
                metrics: {
                    epcRating: "C",
                    annualCost: "£45,000",
                    co2Emissions: "85t",
                    savingPotential: "£12,000"
                },
                insights: [
                    "Historic building retrofit with heritage-compliant solutions",
                    "LED lighting upgrade across all 775 rooms",
                    "Advanced building management system implementation",
                    "Solar panel installation on suitable roof areas"
                ]
            }
        };

        // Initialize Postcode Map
        const postcodeMap = new mapboxgl.Map({
            container: 'postcode-map',
            style: 'mapbox://styles/maoran/clwaku6bv004101pnh8voghhf',
            center: [-3.656229527123442, 53.560791526121],
            zoom: 6,
            attributionControl: false
        });

        // Add navigation controls
        postcodeMap.addControl(new mapboxgl.NavigationControl(), 'top-left');
        postcodeMap.addControl(new mapboxgl.AttributionControl({
            compact: true
        }), 'bottom-left');

        // Set legend
        const postcodeLegend = `
            <h4>Energy Saving Potential</h4>
            <div class="legend-item">
                <span class="legend-color" style="background-color: #2670c5;"></span>
                <span>£155.4K</span>
            </div>
            <div class="legend-item">
                <span class="legend-color" style="background-color: #0abaff;"></span>
                <span>£492.5K</span>
            </div>
            <div class="legend-item">
                <span class="legend-color" style="background-color: #5cd6ff;"></span>
                <span>£673.2K</span>
            </div>
            <div class="legend-item">
                <span class="legend-color" style="background-color: #f5f5f5;"></span>
                <span>£820.9K</span>
            </div>
            <div class="legend-item">
                <span class="legend-color" style="background-color: #ff968f;"></span>
                <span>£1.02M</span>
            </div>
            <div class="legend-item">
                <span class="legend-color" style="background-color: #d93f44;"></span>
                <span>£3.46M</span>
            </div>
        `;
        document.getElementById('postcode-legend').innerHTML = postcodeLegend;

        // Setup postcode map interactions
        postcodeMap.on('load', () => {
            postcodeMap.on('click', 'choropleth-fill', function(e) {
                const properties = e.features[0].properties;
                const popupKeys = ['EnergySaving'];
                let description = "<h3>Postcode Analysis</h3>";
                
                popupKeys.forEach(function(key) {
                    if (properties[key] !== undefined) {
                        const value = `£${Number(properties[key]).toLocaleString()}`;
                        description += `<div><strong>Energy Saving Potential:</strong> ${value}</div>`;
                    }
                });

                new mapboxgl.Popup({
                    closeButton: true,
                    closeOnClick: true,
                    maxWidth: '300px'
                })
                    .setLngLat(e.lngLat)
                    .setHTML(description)
                    .addTo(postcodeMap);
            });

            postcodeMap.on('mouseenter', 'choropleth-fill', function() {
                postcodeMap.getCanvas().style.cursor = 'pointer';
            });

            postcodeMap.on('mouseleave', 'choropleth-fill', function() {
                postcodeMap.getCanvas().style.cursor = '';
            });
        });

        // Building search functionality
        function searchBuilding() {
            const input = document.getElementById('address-input');
            const resultsContainer = document.getElementById('search-results');
            const query = input.value.toLowerCase().trim();

            if (!query) {
                resultsContainer.innerHTML = '<div class="no-results">Please enter an address to search</div>';
                return;
            }

            // Show loading state
            resultsContainer.innerHTML = `
                <div class="loading-search">
                    <div class="search-spinner"></div>
                    <p>Analyzing building energy data...</p>
                </div>
            `;

            // Simulate API call delay
            setTimeout(() => {
                const building = findBuilding(query);
                if (building) {
                    displayBuildingResult(building);
                } else {
                    displayNoResults(query);
                }
            }, 1500);
        }

        function findBuilding(query) {
            // Simple matching for demo purposes
            for (const key in sampleBuildings) {
                if (query.includes(key) || key.includes(query.split(' ')[0])) {
                    return sampleBuildings[key];
                }
            }
            return null;
        }

        function displayBuildingResult(building) {
            const resultsContainer = document.getElementById('search-results');
            
            const insightsHtml = building.insights.map(insight => `
                <div class="insight-item">
                    <div class="insight-icon"></div>
                    <span>${insight}</span>
                </div>
            `).join('');

            resultsContainer.innerHTML = `
                <div class="building-result">
                    <div class="building-address">${building.address}</div>
                    
                    <div class="metrics-grid">
                        <div class="metric-item">
                            <div class="metric-value">${building.metrics.epcRating}</div>
                            <div class="metric-label">EPC Rating</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value">${building.metrics.annualCost}</div>
                            <div class="metric-label">Annual Cost</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value">${building.metrics.co2Emissions}</div>
                            <div class="metric-label">CO₂ Emissions</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value">${building.metrics.savingPotential}</div>
                            <div class="metric-label">Saving Potential</div>
                        </div>
                    </div>

                    <div class="retrofit-insights">
                        <div class="insights-title">Retrofit Recommendations</div>
                        ${insightsHtml}
                    </div>
                </div>
            `;
        }

        function displayNoResults(query) {
            const resultsContainer = document.getElementById('search-results');
            resultsContainer.innerHTML = `
                <div class="no-results">
                    <p>No building data found for "${query}"</p>
                    <p style="margin-top: 0.5rem; font-size: 0.9rem;">Try searching for "10 Downing Street" or "Buckingham Palace" for demo data</p>
                </div>
            `;
        }

        // Allow Enter key to trigger search
        document.getElementById('address-input').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchBuilding();
            }
        });
    </script>
</body>
</html>
